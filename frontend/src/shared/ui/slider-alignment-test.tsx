'use client'

import { useState } from 'react'
import { Slider, type SliderMark } from '@/shared/ui/kit/slider'

export default function SliderAlignmentTest() {
    const [value, setValue] = useState([1])

    const testMarks: SliderMark[] = [
        { value: 0, label: '0' },
        { value: 1, label: '1' },
        { value: 2, label: '2' },
        { value: 3, label: '3' },
    ]

    return (
        <div className="w-full max-w-md mx-auto p-8 space-y-8">
            <div>
                <h2 className="text-lg font-semibold mb-4">Alignment Test</h2>
                <p className="text-sm text-muted-foreground mb-4">
                    Test to verify that marks align perfectly with thumb positions.
                    Move the slider to see if the thumb centers align with the mark dots.
                </p>
                
                <Slider
                    value={value}
                    onValueChange={setValue}
                    min={0}
                    max={3}
                    step={1}
                    marks={testMarks}
                    className="w-full"
                />
                
                <p className="mt-4 text-sm">Current value: {value[0]}</p>
            </div>

            <div>
                <h3 className="text-md font-medium mb-2">Auto-generated marks test</h3>
                <Slider
                    defaultValue={[50]}
                    min={0}
                    max={100}
                    step={25}
                    marks={true}
                    className="w-full"
                />
            </div>

            <div>
                <h3 className="text-md font-medium mb-2">Range slider test</h3>
                <Slider
                    defaultValue={[25, 75]}
                    min={0}
                    max={100}
                    step={25}
                    marks={true}
                    className="w-full"
                />
            </div>
        </div>
    )
}
