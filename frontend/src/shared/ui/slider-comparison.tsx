'use client'

import { useState } from 'react'
import { Slider, type SliderMark } from '@/shared/ui/kit/slider'

export default function SliderComparison() {
    const [value, setValue] = useState([1])

    const marks: SliderMark[] = [
        { value: 0, label: '1 период' },
        { value: 1, label: '2 периода' },
        { value: 2, label: '3 периода' },
        { value: 3, label: '4 периода' },
    ]

    return (
        <div className="w-full max-w-2xl mx-auto p-8 space-y-12">
            <div>
                <h2 className="text-xl font-semibold mb-6">Slider Enhancement Comparison</h2>
                
                {/* New enhanced slider with marks */}
                <div className="space-y-4">
                    <h3 className="text-lg font-medium">✅ Enhanced Slider with Built-in Marks</h3>
                    <p className="text-sm text-muted-foreground">
                        Marks are automatically positioned and aligned with thumb centers.
                        Active marks are highlighted based on the current value.
                    </p>
                    <Slider
                        value={value}
                        onValueChange={setValue}
                        min={0}
                        max={3}
                        step={1}
                        marks={marks}
                        className="w-full"
                    />
                    <p className="text-sm">Current value: {value[0]} ({marks[value[0]]?.label})</p>
                </div>

                {/* Old approach with manual labels */}
                <div className="space-y-4">
                    <h3 className="text-lg font-medium">❌ Old Approach with Manual Labels</h3>
                    <p className="text-sm text-muted-foreground">
                        Labels positioned manually below the slider, requiring separate styling and positioning.
                        No visual connection between slider values and labels.
                    </p>
                    <Slider
                        value={value}
                        onValueChange={setValue}
                        min={0}
                        max={3}
                        step={1}
                        className="w-full"
                    />
                    <div className="mt-4 flex items-center justify-around text-muted-foreground text-xs">
                        {marks.map((mark, i) => (
                            <span
                                key={mark.value}
                                className={`text-center ${i === value[0] ? 'text-foreground font-medium' : ''}`}
                            >
                                {mark.label}
                            </span>
                        ))}
                    </div>
                </div>
            </div>

            <div className="border-t pt-8">
                <h3 className="text-lg font-medium mb-4">Additional Examples</h3>
                
                <div className="space-y-6">
                    <div>
                        <h4 className="font-medium mb-2">Auto-generated marks</h4>
                        <Slider
                            defaultValue={[50]}
                            min={0}
                            max={100}
                            step={25}
                            marks={true}
                        />
                    </div>

                    <div>
                        <h4 className="font-medium mb-2">Range slider with marks</h4>
                        <Slider
                            defaultValue={[25, 75]}
                            min={0}
                            max={100}
                            step={25}
                            marks={true}
                        />
                    </div>

                    <div>
                        <h4 className="font-medium mb-2">Custom memory configuration</h4>
                        <Slider
                            defaultValue={[50]}
                            min={0}
                            max={100}
                            step={25}
                            marks={[
                                { value: 0, label: '1GB' },
                                { value: 25, label: '4GB' },
                                { value: 50, label: '8GB' },
                                { value: 75, label: '16GB' },
                                { value: 100, label: '32GB' },
                            ]}
                        />
                    </div>
                </div>
            </div>
        </div>
    )
}
