'use client'

import { useState } from 'react'
import { Slider, type SliderMark } from '@/shared/ui/kit/slider'

export default function SliderMarksDemo() {
    const [basicValue, setBasicValue] = useState([25])
    const [customValue, setCustomValue] = useState([1])
    const [rangeValue, setRangeValue] = useState([20, 60])

    // Custom marks with labels
    const customMarks: SliderMark[] = [
        { value: 0, label: 'Low' },
        { value: 1, label: 'Medium' },
        { value: 2, label: 'High' },
        { value: 3, label: 'Ultra' },
    ]

    // Performance marks
    const performanceMarks: SliderMark[] = [
        { value: 0, label: '1GB' },
        { value: 25, label: '4GB' },
        { value: 50, label: '8GB' },
        { value: 75, label: '16GB' },
        { value: 100, label: '32GB' },
    ]

    return (
        <div className="w-full max-w-2xl mx-auto p-8 space-y-12">
            <div>
                <h2 className="text-xl font-semibold mb-6">Enhanced Slider with Marks</h2>
                
                {/* Basic marks (auto-generated) */}
                <div className="space-y-4">
                    <h3 className="text-lg font-medium">Auto-generated Marks</h3>
                    <p className="text-sm text-muted-foreground">
                        Setting <code>marks=true</code> generates marks for each step
                    </p>
                    <Slider
                        value={basicValue}
                        onValueChange={setBasicValue}
                        min={0}
                        max={100}
                        step={25}
                        marks={true}
                        className="w-full"
                    />
                    <p className="text-sm">Current value: {basicValue[0]}</p>
                </div>

                {/* Custom marks with labels */}
                <div className="space-y-4">
                    <h3 className="text-lg font-medium">Custom Marks with Labels</h3>
                    <p className="text-sm text-muted-foreground">
                        Custom marks array with value and label properties
                    </p>
                    <Slider
                        value={customValue}
                        onValueChange={setCustomValue}
                        min={0}
                        max={3}
                        step={1}
                        marks={customMarks}
                        className="w-full"
                    />
                    <p className="text-sm">Current value: {customValue[0]} ({customMarks[customValue[0]]?.label})</p>
                </div>

                {/* Performance/Memory marks */}
                <div className="space-y-4">
                    <h3 className="text-lg font-medium">Memory Configuration</h3>
                    <p className="text-sm text-muted-foreground">
                        Marks with memory size labels
                    </p>
                    <Slider
                        value={rangeValue}
                        onValueChange={setRangeValue}
                        min={0}
                        max={100}
                        step={25}
                        marks={performanceMarks}
                        className="w-full"
                    />
                    <p className="text-sm">
                        Range: {rangeValue[0]} - {rangeValue[1]} 
                        ({performanceMarks.find(m => m.value === rangeValue[0])?.label} - {performanceMarks.find(m => m.value === rangeValue[1])?.label})
                    </p>
                </div>

                {/* No marks (original behavior) */}
                <div className="space-y-4">
                    <h3 className="text-lg font-medium">No Marks (Original)</h3>
                    <p className="text-sm text-muted-foreground">
                        Default slider without marks
                    </p>
                    <Slider
                        defaultValue={[50]}
                        min={0}
                        max={100}
                        step={1}
                        className="w-full"
                    />
                </div>
            </div>
        </div>
    )
}
