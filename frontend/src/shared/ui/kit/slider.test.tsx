import { render, screen } from '@testing-library/react'
import { Slider, type SliderMark } from './slider'

describe('Slider with Marks', () => {
    it('renders without marks by default', () => {
        render(<Slider defaultValue={[50]} />)
        
        // Should not have any mark elements
        expect(screen.queryByText('50')).not.toBeInTheDocument()
    })

    it('renders auto-generated marks when marks=true', () => {
        render(
            <Slider 
                defaultValue={[25]} 
                min={0} 
                max={100} 
                step={25} 
                marks={true} 
            />
        )
        
        // Should have mark dots for each step
        const markContainer = document.querySelector('.relative.mt-2')
        expect(markContainer).toBeInTheDocument()
        
        // Should have 5 marks (0, 25, 50, 75, 100)
        const markDots = document.querySelectorAll('.w-1\\.5.h-1\\.5.rounded-full')
        expect(markDots).toHaveLength(5)
    })

    it('renders custom marks with labels', () => {
        const customMarks: SliderMark[] = [
            { value: 0, label: 'Low' },
            { value: 50, label: 'Medium' },
            { value: 100, label: 'High' },
        ]

        render(
            <Slider 
                defaultValue={[50]} 
                min={0} 
                max={100} 
                marks={customMarks} 
            />
        )
        
        // Should render all labels
        expect(screen.getByText('Low')).toBeInTheDocument()
        expect(screen.getByText('Medium')).toBeInTheDocument()
        expect(screen.getByText('High')).toBeInTheDocument()
        
        // Should have 3 marks
        const markDots = document.querySelectorAll('.w-1\\.5.h-1\\.5.rounded-full')
        expect(markDots).toHaveLength(3)
    })

    it('renders marks without labels', () => {
        const marksWithoutLabels: SliderMark[] = [
            { value: 0 },
            { value: 50 },
            { value: 100 },
        ]

        render(
            <Slider 
                defaultValue={[50]} 
                min={0} 
                max={100} 
                marks={marksWithoutLabels} 
            />
        )
        
        // Should have mark dots but no labels
        const markDots = document.querySelectorAll('.w-1\\.5.h-1\\.5.rounded-full')
        expect(markDots).toHaveLength(3)
        
        // Should not have any label text
        expect(screen.queryByText('0')).not.toBeInTheDocument()
        expect(screen.queryByText('50')).not.toBeInTheDocument()
        expect(screen.queryByText('100')).not.toBeInTheDocument()
    })

    it('applies correct positioning to marks', () => {
        const marks: SliderMark[] = [
            { value: 0, label: 'Start' },
            { value: 50, label: 'Middle' },
            { value: 100, label: 'End' },
        ]

        render(
            <Slider 
                defaultValue={[50]} 
                min={0} 
                max={100} 
                marks={marks} 
            />
        )
        
        const markContainers = document.querySelectorAll('.absolute.flex.flex-col.items-center')
        
        // First mark should be at 0%
        expect(markContainers[0]).toHaveStyle('left: 0%')
        
        // Second mark should be at 50%
        expect(markContainers[1]).toHaveStyle('left: 50%')
        
        // Third mark should be at 100%
        expect(markContainers[2]).toHaveStyle('left: 100%')
    })

    it('adds bottom margin when marks are present', () => {
        const { rerender } = render(<Slider defaultValue={[50]} />)
        
        // Without marks, should not have mb-6 class
        let sliderRoot = document.querySelector('[data-slot="slider"]')
        expect(sliderRoot).not.toHaveClass('mb-6')
        
        // With marks, should have mb-6 class
        rerender(<Slider defaultValue={[50]} marks={true} min={0} max={100} step={50} />)
        sliderRoot = document.querySelector('[data-slot="slider"]')
        expect(sliderRoot).toHaveClass('mb-6')
    })
})
