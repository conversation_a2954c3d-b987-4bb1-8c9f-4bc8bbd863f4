import * as React from 'react'
import * as SliderPrimitive from '@radix-ui/react-slider'

import { cn } from '@/shared/lib/css'

// Type definitions for marks functionality
export interface SliderMark {
    value: number
    label?: React.ReactNode
}

export interface SliderProps extends React.ComponentProps<typeof SliderPrimitive.Root> {
    marks?: boolean | SliderMark[]
}

function Slider({
    className,
    defaultValue,
    value,
    min = 0,
    max = 100,
    marks = false,
    ...props
}: SliderProps) {
    const _values = React.useMemo(
        () =>
            Array.isArray(value) ? value : Array.isArray(defaultValue) ? defaultValue : [min, max],
        [
            value,
            defaultValue,
            min,
            max,
        ],
    )

    // Generate marks based on the marks prop
    const _marks = React.useMemo(() => {
        if (marks === false) return []

        if (marks === true) {
            // Generate marks for each step
            const step = props.step || 1
            const markValues: SliderMark[] = []
            for (let i = min; i <= max; i += step) {
                markValues.push({ value: i })
            }
            return markValues
        }

        // Use provided marks array
        return marks
    }, [
        marks,
        min,
        max,
        props.step,
    ])

    // Calculate mark position to align with thumb center
    const getMarkPosition = (markValue: number) => {
        const percentage = ((markValue - min) / (max - min)) * 100
        return percentage
    }

    // Check if a mark is active (within the current value range)
    const isMarkActive = (markValue: number) => {
        if (Array.isArray(_values)) {
            return markValue >= Math.min(..._values) && markValue <= Math.max(..._values)
        }
        return markValue <= (_values[0] || min)
    }

    return (
        <div className="relative w-full">
            <SliderPrimitive.Root
                data-slot="slider"
                defaultValue={defaultValue}
                value={value}
                min={min}
                max={max}
                className={cn(
                    'relative flex w-full touch-none items-center select-none data-[disabled]:opacity-50 data-[orientation=vertical]:h-full data-[orientation=vertical]:min-h-44 data-[orientation=vertical]:w-auto data-[orientation=vertical]:flex-col',
                    _marks.length > 0 && 'mb-6', // Add bottom margin when marks are present
                    className,
                )}
                {...props}
            >
                <SliderPrimitive.Track
                    data-slot="slider-track"
                    className={cn(
                        'bg-white relative grow overflow-hidden rounded-full data-[orientation=horizontal]:h-1.5 data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-1.5',
                    )}
                >
                    <SliderPrimitive.Range
                        data-slot="slider-range"
                        className={cn(
                            'bg-primary/50 absolute data-[orientation=horizontal]:h-full data-[orientation=vertical]:w-full',
                        )}
                    />
                </SliderPrimitive.Track>
                {Array.from({ length: _values.length }, (_, index) => (
                    <SliderPrimitive.Thumb
                        data-slot="slider-thumb"
                        key={index}
                        className="border-primary bg-background ring-ring/50 block size-7 shrink-0 rounded-full border-3 shadow-sm transition-[color,box-shadow] hover:ring-4 focus-visible:ring-4 focus-visible:outline-hidden disabled:pointer-events-none disabled:opacity-50"
                    />
                ))}
            </SliderPrimitive.Root>

            {/* Render marks below the slider */}
            {_marks.length > 0 && (
                <div className="relative mt-2 px-3.5">
                    {_marks.map((mark) => (
                        <div
                            key={`mark-${mark.value}`}
                            className="absolute flex flex-col items-center"
                            style={{
                                left: `${getMarkPosition(mark.value)}%`,
                                transform: 'translateX(-50%)',
                            }}
                        >
                            {/* Mark dot */}
                            <div
                                className={cn(
                                    'w-1.5 h-1.5 rounded-full border transition-colors',
                                    isMarkActive(mark.value)
                                        ? 'bg-primary border-primary'
                                        : 'bg-white border-gray-300',
                                )}
                            />
                            {/* Mark label */}
                            {mark.label && (
                                <span
                                    className={cn(
                                        'mt-1 text-xs transition-colors',
                                        isMarkActive(mark.value)
                                            ? 'text-foreground'
                                            : 'text-muted-foreground',
                                    )}
                                >
                                    {mark.label}
                                </span>
                            )}
                        </div>
                    ))}
                </div>
            )}
        </div>
    )
}

export { Slider }
