# Enhanced Slider Component

The enhanced Slider component extends the ShadCN/Radix UI slider primitive with marks functionality inspired by Material-UI's slider implementation.

## Features

- **Backward Compatible**: All existing slider functionality remains unchanged
- **Auto-generated Marks**: Set `marks={true}` to automatically generate marks for each step
- **Custom Marks**: Provide an array of mark objects with values and optional labels
- **Mark Labels**: Display text labels below marks for better user guidance
- **Active State**: Marks within the current value range are visually highlighted
- **TypeScript Support**: Full type definitions for all new props

## Basic Usage

### Without Marks (Original Behavior)
```tsx
<Slider defaultValue={[50]} min={0} max={100} />
```

### Auto-generated Marks
```tsx
<Slider 
  defaultValue={[25]} 
  min={0} 
  max={100} 
  step={25} 
  marks={true} 
/>
```

### Custom Marks with Labels
```tsx
const marks = [
  { value: 0, label: 'Low' },
  { value: 50, label: 'Medium' },
  { value: 100, label: 'High' },
]

<Slider 
  defaultValue={[50]} 
  min={0} 
  max={100} 
  marks={marks} 
/>
```

### Custom Marks without Labels
```tsx
const marks = [
  { value: 0 },
  { value: 25 },
  { value: 50 },
  { value: 75 },
  { value: 100 },
]

<Slider 
  defaultValue={[50]} 
  min={0} 
  max={100} 
  marks={marks} 
/>
```

## Props

### New Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `marks` | `boolean \| SliderMark[]` | `false` | Controls mark display. `true` generates marks for each step, array provides custom marks |

### SliderMark Interface

```tsx
interface SliderMark {
  value: number        // The value position for the mark
  label?: React.ReactNode  // Optional label to display below the mark
}
```

## Examples

### Memory Configuration Slider
```tsx
const memoryMarks = [
  { value: 0, label: '1GB' },
  { value: 25, label: '4GB' },
  { value: 50, label: '8GB' },
  { value: 75, label: '16GB' },
  { value: 100, label: '32GB' },
]

<Slider 
  value={memoryValue}
  onValueChange={setMemoryValue}
  min={0}
  max={100}
  step={25}
  marks={memoryMarks}
/>
```

### Quality Settings
```tsx
const qualityMarks = [
  { value: 0, label: 'Low' },
  { value: 1, label: 'Medium' },
  { value: 2, label: 'High' },
  { value: 3, label: 'Ultra' },
]

<Slider 
  value={quality}
  onValueChange={setQuality}
  min={0}
  max={3}
  step={1}
  marks={qualityMarks}
/>
```

### Range Slider with Marks
```tsx
<Slider 
  value={[20, 80]}
  onValueChange={setRange}
  min={0}
  max={100}
  step={10}
  marks={true}
/>
```

## Styling

The component uses Tailwind CSS classes and follows the existing ShadCN design system:

- **Mark Dots**: Small circular indicators positioned along the track
- **Active Marks**: Highlighted when within the current value range
- **Labels**: Positioned below marks with responsive text sizing
- **Spacing**: Automatic bottom margin added when marks are present

## Migration from Manual Labels

If you were previously implementing manual labels below sliders (like in the existing codebase), you can now use the built-in marks functionality:

### Before
```tsx
<Slider defaultValue={[1]} max={3} step={1} />
<div className="mt-4 flex items-center justify-around text-muted-foreground text-xs">
  {labels.map((label, i) => (
    <span key={label}>{label}</span>
  ))}
</div>
```

### After
```tsx
const marks = [
  { value: 0, label: 'Label 1' },
  { value: 1, label: 'Label 2' },
  { value: 2, label: 'Label 3' },
  { value: 3, label: 'Label 4' },
]

<Slider defaultValue={[1]} min={0} max={3} step={1} marks={marks} />
```

## Accessibility

The enhanced slider maintains all accessibility features from the Radix UI primitive while adding visual enhancements that improve usability for all users.
