import type { <PERSON>a, <PERSON>Obj } from '@storybook/react-vite'

import { Slider, type SliderMark } from './slider'
import { useState } from 'react'
import clsx from 'clsx'

const meta = {
    component: Slider,
} satisfies Meta<typeof Slider>

export default meta

type Story = StoryObj<typeof meta>

function DefaultComponent() {
    const [value, setValue] = useState([1])
    const ramExpansions = [
        '1 период',
        '2 периода',
        '3 периода',
        '4 периода',
    ]

    return (
        <>
            <Slider
                defaultValue={[1]}
                max={3}
                step={1}
                value={value}
                onValueChange={setValue}
            />
            <div className="mt-4 flex items-center justify-around text-muted-foreground text-xs">
                {ramExpansions.map((expansion, i) => (
                    <span
                        key={expansion}
                        className={clsx(i !== value[0] && 'text-red-300', 'text-center')}
                    >
                        {expansion}
                    </span>
                ))}
            </div>
        </>
    )
}

export const Default: Story = {
    render: () => <DefaultComponent />,
}

function WithMarksComponent() {
    const [value, setValue] = useState([1])

    const marks: SliderMark[] = [
        { value: 0, label: '1 период' },
        { value: 1, label: '2 периода' },
        { value: 2, label: '3 периода' },
        { value: 3, label: '4 периода' },
    ]

    return (
        <div className="w-full max-w-md">
            <Slider
                value={value}
                onValueChange={setValue}
                min={0}
                max={3}
                step={1}
                marks={marks}
            />
        </div>
    )
}

export const WithMarks: Story = {
    render: () => <WithMarksComponent />,
}

function AutoMarksComponent() {
    const [value, setValue] = useState([25])

    return (
        <div className="w-full max-w-md">
            <Slider
                value={value}
                onValueChange={setValue}
                min={0}
                max={100}
                step={25}
                marks={true}
            />
            <p className="mt-2 text-sm text-muted-foreground">Value: {value[0]}</p>
        </div>
    )
}

export const AutoMarks: Story = {
    render: () => <AutoMarksComponent />,
}

function RangeWithMarksComponent() {
    const [value, setValue] = useState([20, 60])

    const memoryMarks: SliderMark[] = [
        { value: 0, label: '1GB' },
        { value: 25, label: '4GB' },
        { value: 50, label: '8GB' },
        { value: 75, label: '16GB' },
        { value: 100, label: '32GB' },
    ]

    return (
        <div className="w-full max-w-md">
            <Slider
                value={value}
                onValueChange={setValue}
                min={0}
                max={100}
                step={25}
                marks={memoryMarks}
            />
            <p className="mt-2 text-sm text-muted-foreground">
                Range: {value[0]} - {value[1]}
            </p>
        </div>
    )
}

export const RangeWithMarks: Story = {
    render: () => <RangeWithMarksComponent />,
}
